{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "oiPCILC9Y9ye88tkN4f+XFo3xxlWQOZ7iXBzQ8fSiPw="}}}, "functions": {}, "sortedMiddleware": ["/"]}