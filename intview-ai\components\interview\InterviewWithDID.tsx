"use client";
import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRight, CheckCircle, Mic } from "lucide-react";
import JobInfoCard from "@/components/JobInfoCard";
import QuestionsList from "@/components/QuestionsList";
import InterviewLayout from "@/components/InterviewLayout";
import VoiceRecognition from "@/components/interview/VoiceRecognition";
import { useInterview } from "@/context/InterviewContext";

type InterviewWithDIDProps = {
  onNext?: () => void;
};

const InterviewWithDID: React.FC<InterviewWithDIDProps> = ({
  onNext,
}) => {
  const {
    currentQuestion,
    isInterviewStarted,
    isLoading,
    startInterview: startInterviewAPI,
    submitAnswer,
    error,
    isInterviewCompleted,
    interviewSummary,
  } = useInterview();

  const [showSubmitButton, setShowSubmitButton] = useState<boolean>(false);
  const [candidateAnswer, setCandidateAnswer] = useState<string>("");
  const [inputMode, setInputMode] = useState<"text" | "voice">("voice"); 

  const startInterview = async () => {
    try {
      await startInterviewAPI();
      setShowSubmitButton(true);
    } catch (err) {
      console.error("Failed to start interview:", err);
    }
  };

  // Voice recognition handlers
  const handleTranscriptChange = (transcript: string) => {
    setCandidateAnswer(transcript);
  };

  const handleFinalTranscript = (transcript: string) => {
    setCandidateAnswer(transcript);
  };

  const toggleInputMode = () => {
    setInputMode(inputMode === "voice" ? "text" : "voice");
    setCandidateAnswer(""); 
  };




  const handleSubmitAnswer = async () => {
    if (!candidateAnswer.trim()) {
      alert("Please provide an answer before continuing.");
      return;
    }

    try {
      setShowSubmitButton(false);
      await submitAnswer(candidateAnswer);
      setCandidateAnswer("");
      setShowSubmitButton(true);
    } catch (err) {
      console.error("Failed to submit answer:", err);
      setShowSubmitButton(true);
    }
  };

  if (!isInterviewStarted) {
    return (
      <div className="h-screen">
        <JobInfoCard />
        <InterviewLayout>
          <div className="flex flex-col md:flex-row gap-10 justify-center items-center md:items-start">
            <QuestionsList className="h-[550px]" />
            {/* <CandidateWithAgent
              className="w-[300px] h-[300px]"
              // useAgent={true}
              candidateName={candidateName}
              jobTitle={jobTitle}
            /> */}
          </div>

          <div className="flex justify-center mt-10 gap-4">
            <Button
              variant="default"
              className="py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white"
              onClick={startInterview}
            >
              Start Interview
              <ArrowRight className="w-6 h-6 duration-300 group-hover:translate-x-1" />
            </Button>
          </div>
          
          <div className="flex justify-center mt-5 text-2xl font-semibold text-primary">
            Ready to begin
          </div>
        </InterviewLayout>
      </div>
    );
  }

  if (isInterviewCompleted) {
    return (
      <div className="h-screen">
        <JobInfoCard />
        <InterviewLayout>
          <div className="flex flex-col items-center justify-center h-full">
            <div className="text-center mb-8">
              <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-gray-800 mb-2">
                Interview Completed!
              </h2>
              <p className="text-gray-600 mb-4">
                Thank you for completing the interview. Your responses have been recorded.
              </p>

              {interviewSummary && (
                <div className="bg-white rounded-lg shadow-lg p-6 max-w-md mx-auto mb-6">
                  <h3 className="text-lg font-semibold mb-4">Detailed Interview Summary</h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Technical Skills:</span>
                      <span className="font-medium">{interviewSummary.ScoreCard.technicalSkills}/20</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Problem Solving:</span>
                      <span className="font-medium">{interviewSummary.ScoreCard.problemSolving}/20</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Communication:</span>
                      <span className="font-medium">{interviewSummary.ScoreCard.communication}/20</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Experience:</span>
                      <span className="font-medium">{interviewSummary.ScoreCard.experience}/20</span>
                    </div>
                    <hr className="my-2" />
                    <div className="flex justify-between font-semibold text-base">
                      <span>Total Score:</span>
                      <span>{interviewSummary.ScoreCard.overall}/100</span>
                    </div>
                    <div className="mt-4">
                      <div className="text-center">
                        <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                          interviewSummary.recommendation === 'HIRE' ? 'bg-green-100 text-green-800' :
                          interviewSummary.recommendation === 'REJECT' ? 'bg-red-100 text-red-800' :
                          'bg-yellow-100 text-yellow-800'
                        }`}>
                          {interviewSummary.recommendation}
                        </span>
                      </div>
                      <p className="text-xs text-gray-600 mt-2">{interviewSummary.reason}</p>
                    </div>
                  </div>
                </div>
              )}
            </div>

            <Button
              variant="default"
              className="py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white"
              onClick={() => onNext?.()}
            >
              View Results
              <ArrowRight className="w-6 h-6 duration-300 group-hover:translate-x-1" />
            </Button>
          </div>
        </InterviewLayout>
      </div>
    );
  }

  return (
    <div className="h-screen">
      <JobInfoCard />
      <InterviewLayout>
        <div className="flex flex-col lg:flex-row gap-10 justify-center items-center lg:items-start">

          <div className="flex-1 max-w-2xl">
            {/* Current Question Display */}
            <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">Question:</h3>
              <p className="text-gray-700 text-lg leading-relaxed">
                {currentQuestion || "Loading question..."}
              </p>
              {error && (
                <div className="mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                  Error: {error}
                </div>
              )}
            </div>

            {/* Answer Input */}
            {showSubmitButton && (
              <div className="bg-white rounded-lg shadow-lg p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-800">Your Answer:</h3>
                  <div className="flex items-center gap-2">
                    <Button
                      variant={inputMode === "voice" ? "default" : "outline"}
                      size="sm"
                      onClick={toggleInputMode}
                      className="flex items-center gap-2 text-white"
                    >
                      <Mic className="w-4 h-4" />
                      Voice
                    </Button>
                   
                  </div>
                </div>

              {inputMode === "voice" ? (
                <VoiceRecognition
                  onTranscriptChange={handleTranscriptChange}
                  onFinalTranscript={handleFinalTranscript}
                  isDisabled={isLoading}
                  className="mb-4"
                />
              ) : (
                <textarea
                  value={candidateAnswer}
                  onChange={(e) => setCandidateAnswer(e.target.value)}
                  placeholder="Type your answer here..."
                  className="w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  rows={4}
                />
              )}

                {/* Show current answer */}
                {/* {inputMode === "voice" && candidateAnswer && (
                  <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <p className="text-blue-700 text-sm">{candidateAnswer}</p>
                  </div>
                )} */}
              </div>
            )}
          </div>
           <QuestionsList className="h-[400px] lg:w-80" />


          {/* <CandidateWithAgent
            className="w-[300px] h-[300px]"
            candidateName={candidateName}
            jobTitle={jobTitle}
            useAgent={false}
          /> */}
        </div>

        <div className="flex justify-center mt-10 gap-4">
          {showSubmitButton && !isLoading ? (
            <Button
              variant="default"
              className="py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white"
              onClick={handleSubmitAnswer}
            >
              {isInterviewCompleted ? "Finish Interview" : "Submit Answer"}
              <ArrowRight className="w-6 h-6 duration-300 group-hover:translate-x-1" />
            </Button>
          ) : (
            <div className="py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center justify-center gap-2 bg-gray-200 text-gray-500">
              {isLoading ? "Loading question..." : "Listen to the question"}
            </div>
          )}
        </div>


      </InterviewLayout>
    </div>
  );
};

export default InterviewWithDID;
