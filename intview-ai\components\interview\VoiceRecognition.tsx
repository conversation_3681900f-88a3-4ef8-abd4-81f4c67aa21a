"use client";
import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Mic, Square } from "lucide-react";

// Import regenerator-runtime for async/await support
import "regenerator-runtime/runtime";

// TypeScript declarations for speech recognition
declare global {
  interface Window {
    SpeechRecognition: typeof SpeechRecognition;
    webkitSpeechRecognition: typeof SpeechRecognition;
  }
}

// Dynamic import to avoid SSR issues
let SpeechRecognition: typeof import("react-speech-recognition").default | null = null;
let useSpeechRecognition: (() => {
  transcript: string;
  listening: boolean;
  resetTranscript: () => void;
  browserSupportsSpeechRecognition: boolean;
  isMicrophoneAvailable: boolean;
}) | null = null;

if (typeof window !== "undefined") {
  try {
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    const speechRecognitionModule = require("react-speech-recognition");
    SpeechRecognition = speechRecognitionModule.default;
    useSpeechRecognition = speechRecognitionModule.useSpeechRecognition;
  } catch {
    // Fallback if module is not available
    SpeechRecognition = null;
    useSpeechRecognition = null;
  }
}

interface VoiceRecognitionProps {
  onTranscriptChange: (transcript: string) => void;
  onFinalTranscript: (transcript: string) => void;
  isDisabled?: boolean;
  className?: string;
}

// Component that uses speech recognition when available
const VoiceRecognitionWithHook: React.FC<VoiceRecognitionProps> = ({
  onTranscriptChange,
  onFinalTranscript,
  isDisabled = false,
  className = "",
}) => {
  const [isRecording, setIsRecording] = useState(false);
  const [hasStarted, setHasStarted] = useState(false);

  // Always call the hook - it should be available when this component renders
  const speechRecognitionData = useSpeechRecognition!();

  const {
    transcript,
    listening,
    resetTranscript,
    browserSupportsSpeechRecognition,
    isMicrophoneAvailable,
  } = speechRecognitionData;

  // Update parent component with transcript changes
  useEffect(() => {
    if (transcript) {
      onTranscriptChange(transcript);
    }
  }, [transcript, onTranscriptChange]);

  // Handle when speech recognition stops
  useEffect(() => {
    if (hasStarted && !listening && transcript) {
      onFinalTranscript(transcript);
      setIsRecording(false);
      setHasStarted(false);
    }
  }, [listening, transcript, hasStarted, onFinalTranscript]);

  // Simple error handling for speech recognition
  useEffect(() => {
    if (!listening) {
      // Handle when listening stops
    }
  }, [listening, isRecording]);

  const startListening = () => {
    if (!SpeechRecognition || !browserSupportsSpeechRecognition || !isMicrophoneAvailable || isDisabled) {
      return;
    }

    resetTranscript();
    setIsRecording(true);
    setHasStarted(true);

    SpeechRecognition.startListening({
      continuous: true,
      language: "en-US",
    });
  };

  const stopListening = () => {
    if (!SpeechRecognition) {
      return;
    }

    SpeechRecognition.stopListening();
    setIsRecording(false);

    // Trigger final transcript if we have content
    if (transcript) {
      onFinalTranscript(transcript);
    }
    setHasStarted(false);
  };

  const clearTranscript = () => {
    resetTranscript();
    onTranscriptChange("");
  };

  if (!browserSupportsSpeechRecognition) {
    return (
      <div className={`bg-red-50 border border-red-200 rounded-lg p-4 ${className}`}>
        <p className="text-red-700 text-sm">
          Your browser doesn&apos;t support speech recognition. Please use a modern browser like Chrome, Edge, or Safari.
        </p>
      </div>
    );
  }

  if (!isMicrophoneAvailable) {
    return (
      <div className={`bg-yellow-50 border border-yellow-200 rounded-lg p-4 ${className}`}>
        <p className="text-yellow-700 text-sm">
          Microphone access is required for voice input. Please allow microphone permissions and refresh the page.
        </p>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Voice Controls */}
      <div className="flex items-center gap-3">
        {!isRecording && (
          <Button
            onClick={startListening}
            disabled={isDisabled}
            variant="default"
            size="sm"
            className="flex items-center gap-2 bg-primary  text-white"
          >
            <Mic className="w-4 h-4" />
            Start Recording
          </Button>
        )}
        {isRecording && (
           <Button
             onClick={stopListening}
             variant="destructive"
             size="sm"
             className="flex items-center gap-2 text-red-400"
           >
             <Square className="w-4 h-4 " />
             Stop Recording
           </Button>

        )}
        {isRecording && (
           <Button
             size="sm"
             className="flex items-center gap-2 text-white"
           >
           <Mic className="w-4 h-4" />
          Listening
           </Button>

        )}



        {transcript && !isRecording && (
          <Button
            onClick={clearTranscript}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            Clear
          </Button>
        )}
      </div>



      {/* Live Transcript Display */}
      {transcript && (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <p className="text-gray-800 text-sm leading-relaxed">
            {transcript}
          </p>
        </div>
      )}
    </div>
  );
};

// Fallback component when speech recognition is not available
const VoiceRecognitionFallback: React.FC<VoiceRecognitionProps> = ({
  className = "",
}) => {
  return (
    <div className={`bg-gray-50 border border-gray-200 rounded-lg p-4 ${className}`}>
      <p className="text-gray-600 text-sm">
        Speech recognition is not available in this environment.
      </p>
    </div>
  );
};

// Main component that handles client-side rendering
const VoiceRecognition: React.FC<VoiceRecognitionProps> = (props) => {
  const [isClient, setIsClient] = useState(false);

  // Ensure we're on the client side
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Show loading state while initializing on client side
  if (!isClient) {
    return (
      <div className={`bg-gray-50 border border-gray-200 rounded-lg p-4 ${props.className || ""}`}>
        <p className="text-gray-600 text-sm">Initializing voice recognition...</p>
      </div>
    );
  }

  // Check if speech recognition is available and render appropriate component
  if (useSpeechRecognition) {
    return <VoiceRecognitionWithHook {...props} />;
  } else {
    return <VoiceRecognitionFallback {...props} />;
  }
};

export default VoiceRecognition;
