import React from "react";
import { CircularProgressbar, buildStyles } from "react-circular-progressbar";
import "react-circular-progressbar/dist/styles.css";

interface CircularRatingProps {
  label: string;
  percent: number;
  color: string;
  trailColor: string;
}

const CircularRating: React.FC<CircularRatingProps> = ({ 
  label, 
  percent, 
  color, 
  trailColor 
}) => {
  // Ensure percent is within 0-100 range
  const clampedPercent = Math.max(0, Math.min(100, percent || 0));
  
  return (
    <div className="flex flex-col items-center space-y-1 mb-2">
      <p className="text-sm font-semibold mb-3">{label}</p>
      <div className="w-32 h-28">
        <CircularProgressbar
          value={clampedPercent}
          text={`${clampedPercent}%`}
          strokeWidth={10}
          styles={buildStyles({
            textSize: "12px",
            pathColor: color,
            textColor: "#5a5a5a",
            trailColor: trailColor,
          })}
        />
      </div>
    </div>
  );
};

export default CircularRating;
