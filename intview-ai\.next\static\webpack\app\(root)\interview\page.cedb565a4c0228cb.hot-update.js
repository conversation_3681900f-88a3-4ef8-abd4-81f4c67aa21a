"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(root)/interview/page",{

/***/ "(app-pages-browser)/./components/interview/Analysis.tsx":
/*!*******************************************!*\
  !*** ./components/interview/Analysis.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_QuestionsList__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/QuestionsList */ \"(app-pages-browser)/./components/QuestionsList.tsx\");\n/* harmony import */ var _components_InterviewLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/InterviewLayout */ \"(app-pages-browser)/./components/InterviewLayout.tsx\");\n/* harmony import */ var _components_VideoTranscript__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/VideoTranscript */ \"(app-pages-browser)/./components/VideoTranscript.tsx\");\n/* harmony import */ var _components_InterviewCard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/InterviewCard */ \"(app-pages-browser)/./components/InterviewCard.tsx\");\n/* harmony import */ var _analysis_ScoreCard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../analysis/ScoreCard */ \"(app-pages-browser)/./components/analysis/ScoreCard.jsx\");\n/* harmony import */ var _context_InterviewContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/context/InterviewContext */ \"(app-pages-browser)/./context/InterviewContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst Analysis = ()=>{\n    _s();\n    const { interviewSummary, totalScore, questionScores, candidateName, jobTitle, isInterviewCompleted } = (0,_context_InterviewContext__WEBPACK_IMPORTED_MODULE_6__.useInterview)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_InterviewCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\interview\\\\Analysis.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_InterviewLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col lg:flex-row gap-6 lg:gap-10 justify-center items-center lg:items-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_QuestionsList__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                            fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\interview\\\\Analysis.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_VideoTranscript__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                            fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\interview\\\\Analysis.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\interview\\\\Analysis.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\interview\\\\Analysis.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_analysis_ScoreCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                interviewSummary: interviewSummary,\n                totalScore: totalScore,\n                questionScores: questionScores,\n                candidateName: candidateName,\n                jobTitle: jobTitle,\n                isInterviewCompleted: isInterviewCompleted\n            }, void 0, false, {\n                fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\interview\\\\Analysis.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\interview\\\\Analysis.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Analysis, \"sYhQXG06YYNzGvHfDmAJMv+SZfA=\", false, function() {\n    return [\n        _context_InterviewContext__WEBPACK_IMPORTED_MODULE_6__.useInterview\n    ];\n});\n_c = Analysis;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Analysis);\nvar _c;\n$RefreshReg$(_c, \"Analysis\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/interview/Analysis.tsx\n"));

/***/ })

});