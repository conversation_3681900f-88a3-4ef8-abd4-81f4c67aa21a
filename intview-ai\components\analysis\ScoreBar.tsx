import React from "react";

interface ScoreBarProps {
  label: string;
  value: number;
  color?: string;
}

const ScoreBar: React.FC<ScoreBarProps> = ({ 
  label, 
  value, 
  color = "bg-orange-500" 
}) => {
  // Ensure value is within 0-100 range
  const clampedValue = Math.max(0, Math.min(100, value || 0));
  
  return (
    <div className="mb-2">
      <div className="flex justify-between text-sm mb-1">
        <span className="mb-1">{label}</span>
        <span>{clampedValue}/100</span>
      </div>
      <div className="w-full bg-gray-200 rounded-full h-2.5">
        <div
          className={`h-2.5 rounded-full transition-all duration-300 ${color}`}
          style={{ width: `${clampedValue}%` }}
        ></div>
      </div>
    </div>
  );
};

export default ScoreBar;
