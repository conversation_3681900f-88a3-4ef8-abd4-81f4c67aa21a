"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(root)/interview/page",{

/***/ "(app-pages-browser)/./components/analysis/ScoreCard.jsx":
/*!*******************************************!*\
  !*** ./components/analysis/ScoreCard.jsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _ScoreBar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ScoreBar */ \"(app-pages-browser)/./components/analysis/ScoreBar.jsx\");\n/* harmony import */ var _CircularRating__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./CircularRating */ \"(app-pages-browser)/./components/analysis/CircularRating.jsx\");\n\n\n\nconst ScoreCard = (param)=>{\n    let { interviewSummary, totalScore, questionScores, candidateName, jobTitle, isInterviewCompleted } = param;\n    // Calculate percentages for display (scores are out of 20, convert to percentage)\n    const getPercentage = (score)=>Math.round(score / 20 * 100);\n    // Use interview data if available, otherwise show default/loading state\n    const scoreCard = interviewSummary === null || interviewSummary === void 0 ? void 0 : interviewSummary.ScoreCard;\n    const overallScore = (scoreCard === null || scoreCard === void 0 ? void 0 : scoreCard.overall) || totalScore || 0;\n    const overallPercentage = scoreCard ? Math.round(overallScore / 100 * 100) : Math.round(totalScore / (questionScores.length * 20) * 100) || 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 border p-6 rounded-xl w-full max-w-6xl mx-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg p-4 shadow-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between font-semibold mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Interview Score\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\analysis\\\\ScoreCard.jsx\",\n                                lineNumber: 25,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    overallPercentage,\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\analysis\\\\ScoreCard.jsx\",\n                                lineNumber: 26,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\analysis\\\\ScoreCard.jsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ScoreBar__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                label: \"Technical Skills\",\n                                value: scoreCard ? getPercentage(scoreCard.technicalSkills) : 0\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\analysis\\\\ScoreCard.jsx\",\n                                lineNumber: 29,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ScoreBar__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                label: \"Problem Solving\",\n                                value: scoreCard ? getPercentage(scoreCard.problemSolving) : 0,\n                                color: \"bg-purple-600\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\analysis\\\\ScoreCard.jsx\",\n                                lineNumber: 33,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ScoreBar__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                label: \"Communication\",\n                                value: scoreCard ? getPercentage(scoreCard.communication) : 0\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\analysis\\\\ScoreCard.jsx\",\n                                lineNumber: 38,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ScoreBar__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                label: \"Experience\",\n                                value: scoreCard ? getPercentage(scoreCard.experience) : 0\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\analysis\\\\ScoreCard.jsx\",\n                                lineNumber: 42,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\analysis\\\\ScoreCard.jsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 font-medium flex justify-between bg-gray-100 text-sm text-center border rounded-xl p-8\",\n                        children: [\n                            \"Overall Score \\xa0 \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-black\",\n                                children: [\n                                    overallScore,\n                                    \"/100\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\analysis\\\\ScoreCard.jsx\",\n                                lineNumber: 49,\n                                columnNumber: 32\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\analysis\\\\ScoreCard.jsx\",\n                        lineNumber: 48,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\analysis\\\\ScoreCard.jsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg p-4 shadow-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"font-semibold mb-4\",\n                        children: \"Video Score\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\analysis\\\\ScoreCard.jsx\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ScoreBar__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                label: \"Professionalism\",\n                                value: 64\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\analysis\\\\ScoreCard.jsx\",\n                                lineNumber: 57,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ScoreBar__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                label: \"Energy Level\",\n                                value: 56,\n                                color: \"bg-purple-600\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\analysis\\\\ScoreCard.jsx\",\n                                lineNumber: 58,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ScoreBar__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                label: \"Communication\",\n                                value: 58\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\analysis\\\\ScoreCard.jsx\",\n                                lineNumber: 59,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ScoreBar__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                label: \"Sociability\",\n                                value: 70\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\analysis\\\\ScoreCard.jsx\",\n                                lineNumber: 60,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\analysis\\\\ScoreCard.jsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\analysis\\\\ScoreCard.jsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg p-4 flex flex-col space-y-2   gap-5 shadow-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"font-semibold\",\n                        children: \"AI Rating\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\analysis\\\\ScoreCard.jsx\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CircularRating__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        label: \"AI Resume Rating\",\n                        percent: 75,\n                        color: \"#A855F7\",\n                        trailColor: \"#EAE2FF\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\analysis\\\\ScoreCard.jsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CircularRating__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        label: \"AI Video Rating\",\n                        percent: 75,\n                        color: \"#FF5B00\",\n                        trailColor: \"#FFEAE1\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\analysis\\\\ScoreCard.jsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\analysis\\\\ScoreCard.jsx\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\analysis\\\\ScoreCard.jsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, undefined);\n};\n_c = ScoreCard;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ScoreCard);\nvar _c;\n$RefreshReg$(_c, \"ScoreCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/analysis/ScoreCard.jsx\n"));

/***/ })

});