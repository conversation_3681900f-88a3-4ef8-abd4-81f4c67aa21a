import React from "react";
import ScoreBar from "./ScoreBar";
import CircularRating from "./CircularRating";
import { Summary } from "@/lib/interview-api";

interface ScoreCardProps {
  interviewSummary?: Summary | null;
  totalScore?: number;
  questionScores?: number[];
  candidateName?: string;
  jobTitle?: string;
  isInterviewCompleted?: boolean;
}

const ScoreCard: React.FC<ScoreCardProps> = ({
  interviewSummary,
  totalScore = 0,
  questionScores = [],
  candidateName = "",
  jobTitle = "",
  isInterviewCompleted = false
}) => {
  // Calculate percentages for display (scores are out of 20, convert to percentage)
  const getPercentage = (score: number): number => Math.round((score / 20) * 100);
  
  // Use interview data if available, otherwise show default/loading state
  const scoreCard = interviewSummary?.ScoreCard;
  const overallScore = scoreCard?.overall || totalScore || 0;
  const overallPercentage = scoreCard 
    ? Math.round((overallScore / 100) * 100) 
    : Math.round((totalScore / (questionScores.length * 20)) * 100) || 0;

  // Calculate AI ratings based on interview performance
  const aiResumeRating = scoreCard ? getPercentage(scoreCard.experience) : 0;
  const aiInterviewRating = scoreCard 
    ? Math.round(((scoreCard.communication + scoreCard.technicalSkills) / 40) * 100)
    : overallPercentage;

  return (
    <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 border p-6 rounded-xl w-full max-w-6xl mx-auto">
      {/* Interview Score */}
      <div className="bg-white rounded-lg p-4 shadow-sm">
        <div className="flex justify-between font-semibold mb-4">
          <span>Interview Score</span>
          <span>{overallPercentage}%</span>
        </div>
        <div className="flex flex-col gap-4">
          <ScoreBar
            label="Technical Skills"
            value={scoreCard ? getPercentage(scoreCard.technicalSkills) : 0}
          />
          <ScoreBar
            label="Problem Solving"
            value={scoreCard ? getPercentage(scoreCard.problemSolving) : 0}
            color="bg-purple-600"
          />
          <ScoreBar
            label="Communication"
            value={scoreCard ? getPercentage(scoreCard.communication) : 0}
          />
          <ScoreBar
            label="Experience"
            value={scoreCard ? getPercentage(scoreCard.experience) : 0}
          />
        </div>

        <div className="mt-4 font-medium flex justify-between bg-gray-100 text-sm text-center border rounded-xl p-8">
          Overall Score &nbsp; <span className="text-black">{overallScore}/100</span>
        </div>
      </div>

      {/* Performance Summary */}
      <div className="bg-white rounded-lg p-4 shadow-sm">
        <div className="font-semibold mb-4">Performance Summary</div>
        <div className="flex flex-col gap-4">
          <div className="text-sm">
            <div className="flex justify-between mb-2">
              <span>Questions Answered:</span>
              <span className="font-medium">{questionScores.length}</span>
            </div>
            <div className="flex justify-between mb-2">
              <span>Average Score:</span>
              <span className="font-medium">
                {questionScores.length > 0 
                  ? Math.round(totalScore / questionScores.length) 
                  : 0}/20
              </span>
            </div>
            <div className="flex justify-between mb-2">
              <span>Status:</span>
              <span className={`font-medium ${
                isInterviewCompleted ? 'text-green-600' : 'text-orange-600'
              }`}>
                {isInterviewCompleted ? 'Completed' : 'In Progress'}
              </span>
            </div>
          </div>
          
          {interviewSummary && (
            <div className="mt-4 p-3 bg-gray-50 rounded-lg">
              <div className="text-sm font-medium mb-2">Recommendation:</div>
              <div className={`text-sm font-semibold mb-2 ${
                interviewSummary.recommendation === 'HIRE' ? 'text-green-600' :
                interviewSummary.recommendation === 'REJECT' ? 'text-red-600' :
                'text-orange-600'
              }`}>
                {interviewSummary.recommendation}
              </div>
              <div className="text-xs text-gray-600">
                {interviewSummary.reason}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* AI Ratings */}
      <div className="bg-white rounded-lg p-4 flex flex-col space-y-2 gap-5 shadow-sm">
        <p className="font-semibold">AI Rating</p>
        <CircularRating
          label="AI Experience Rating"
          percent={aiResumeRating}
          color="#A855F7"
          trailColor="#EAE2FF"
        />
        <CircularRating
          label="AI Interview Rating"
          percent={aiInterviewRating}
          color="#FF5B00"
          trailColor="#FFEAE1"
        />
      </div>
    </div>
  );
};

export default ScoreCard;
