"use client";
import QuestionsList from "@/components/QuestionsList";
import InterviewLayout from "@/components/InterviewLayout";
import VideoTranscript from "@/components/VideoTranscript";
import InterviewCard from "@/components/InterviewCard";
import ScoreCard from "../analysis/ScoreCard";
import { useInterview } from "@/context/InterviewContext";

const Analysis = () => {
  const {
    interviewSummary,
    totalScore,
    questionScores,
    candidateName,
    jobTitle,
    isInterviewCompleted
  } = useInterview();

  return (
    <div className="h-screen">
      <InterviewCard />
      <InterviewLayout>
        <div className="flex flex-col lg:flex-row gap-6 lg:gap-10 justify-center items-center lg:items-start">
          <QuestionsList />
          {/* <CandidateWithAgent
            className="h-[490px]"
            useAgent={false}
            candidateName="Jonathan"
            jobTitle="Insurance Agent"
          /> */}
          <VideoTranscript />
        </div>
      </InterviewLayout>
      <ScoreCard
        interviewSummary={interviewSummary}
        totalScore={totalScore}
        questionScores={questionScores}
        candidateName={candidateName}
        jobTitle={jobTitle}
        isInterviewCompleted={isInterviewCompleted}
      />
    </div>
  );
};

export default Analysis;
